import { FxElement, html } from '/fx.js';
import { $styles } from './jlink.x.js';
import '../button/button.js';

export class FxJLink extends FxElement {
    static properties = {
        base: { type: Object },
        isReady: { type: Boolean, default: false },
        mediaPath: { type: String, default: 'http://192.168.192.2:8001' },
        cell: { type: Object, default: undefined, notify: true },
        url: { type: String, default: '', notify: true },
        ext: { type: String, default: '...' },
        pdfjsView: { type: Boolean, default: false, save: true },
        pdfjsViewUrl: { type: String, default: 'http://192.168.192.2:8001/~apps~/pdf.js/web/viewer.html?file=' }
    }
    get isPDF() {
        return this.ext === 'pdf' || this.cell?.url?.endsWith('.pdf')
    }
    get isMD() {
        let res = this.ext === 'md' || this.cell?.url?.endsWith('.md') || this.linkType === 'markdown';
        if (res)
            import('../jmd/jmd.js');
        return res;
    }
    get isEPUB() {
        let res = this.ext === 'epub' || this.cell?.url?.endsWith('.epub') || this.linkType === 'epub';
        if (res)
            import('../jepub/jepub.js');
        return res;
    }
    get isHTML() {
        return this.ext === 'html' || this.cell?.url?.endsWith('.html') || this.linkType === 'html';
    }
    get iframeSrc() {
        if (this.isPDF && this.pdfjsView) {
            if (this.isAttachments) {
                return '/fx/~/pdf.js/web/viewer.html?file=' + this.checkedURL;
            }
            const viewer = FX.$url.replace('fx.js', 'fx/~/pdf.js/web/viewer.html?file=')
            let url = this.isLoadFile ? viewer : (this.pdfjsViewUrl || viewer)
            url += this.checkedURL;
            console.log(url);
            return url;
        }
        return this.checkedURL;
    }
    get isAttachments() { return this.cell?.attachments }

    async firstUpdated() {
        super.firstUpdated();
        setTimeout(async () => {
            await this.init();
            this.isReady = true;
            import('../jmd/jmd.js')
            this.$update();
            setTimeout(() => {
                this.style.opacity = 1;
            }, 100)
        }, 100)
    }

    'url-changed'(e) {
        if (!e) return;
        if (this.isReady && !this.isAttachments && !this.isLoadFile) {
            // console.log('url-changed', e);
            this.checkURL(e);
            this.$update();
        }
    }
    async init() {
        if (this.isAttachments) {
            try {
                let db = this.db = this.base.dbLocal;
                let _id = this._idFile = this.cell?._idFile || 'file:' + this.base.bsSelected._id + '-' + this.cell.ulid;
                let doc;
                try { doc = await db.get(_id, { attachments: true }) } catch (error) { }
                if (!doc) {
                    _id = this._idFile = 'file:' + this.base.bsSelected._id;
                    try { doc = await db.get(_id, { attachments: true }) } catch (error) { }
                }
                if (doc) {
                    let url = 'data:' + doc._attachments.file.content_type + ';base64,' + doc._attachments.file.data
                    this.setLinkType(doc._attachments.file.content_type);
                    let res = await fetch(url);
                    let blob = await res.blob();
                    this.checkedURL = URL.createObjectURL(blob, { type: 'data:text' });
                    this.$update();
                }
            } catch (error) { }
        } else {
            this.checkURL(this.cell?.url || this.url || this.cell?.source);
        }
    }
    checkURL(url) {
        url = FX.checkUrl(url);
        this.checkedURL = url;
        this.ext = '...';
        if (!this.isHTML && this.cell?.cell_type === 'html' && this.cell?.source) {
            const text = this.cell?.source;
            const blob = new Blob([text], { type: 'text/html' });
            const blobURL = URL.createObjectURL(blob);
            this.checkedURL = blobURL;
            this.linkType = 'html';
            return;
        }
        this.setLinkTypeUrl();
    }
    setLinkTypeUrl(url = this.checkedURL) {
        this.linkType = 'file';
        let ext;
        try {
            ext = this.ext = url?.split('?')[0].split('.').at(-1).toLowerCase()
        } catch (err) { console.log(err) }
        if (ext === 'pdf') {
            this.linkType = 'file';
        } else if (ext === 'html') {
            this.linkType = 'html';
        } else if (['webp', 'png', 'jpg', 'jpeg', 'gif', 'bmp', 'svg', 'apng'].includes(ext)) {
            this.linkType = 'image';
        } else if (['mp4', 'mkv', 'ogv', 'webm', 'mov', 'avi'].includes(ext)) {
            this.linkType = 'video';
        } else if (['mp3', 'wav', 'flac', 'ogg'].includes(ext)) {
            this.linkType = 'audio';
        } else if (ext === 'md') {
            this.linkType = 'markdown';
        } else if (ext === 'epub') {
            this.linkType = 'epub';
        }
        this.$update();
    }
    setLinkType(type) {
        this.linkType = 'file';
        if (type.includes('image/')) {
            this.linkType = 'image';
        } else if (type.includes('video/')) {
            this.linkType = 'video';
        } else if (type.includes('audio/')) {
            this.linkType = 'audio';
        } else if (type === 'text/markdown' || type === 'text/x-markdown') {
            this.linkType = 'markdown';
        } else if (type === 'application/epub+zip') {
            this.linkType = 'epub';
        } else if (type === 'application/pdf') {
            this.linkType = 'file'; // PDF обрабатывается как файл с особым просмотром
        } else if (type === 'text/html') {
            this.linkType = 'html';
        }
    }
    async loadFile(e) {
        const file = e.target?.files[0];
        if (!file) return;
        this.deleteFile();
        this.setLinkTypeUrl(file.name);
        if (this.linkType === 'file')
            this.setLinkType(file.type);
        if (!this.isHTML && this.cell?.cell_type === 'html' && this.cell?.source) // ???
            return;
        this.isLoadFile = true;
        this.url = file.name;
        if (this.cell) {
            this.cell.size = file.size;
        }
        const reader = new FileReader();
        if (this.linkType === 'epub') { // ???
            this.source = '';
            reader.onload = async (e) => {
                this.checkedURL = this.source = e.target.result;
                if (this.cell) {
                    this.cell.source = this.source;
                    this.cell.url = this.url;
                }
                this.$update();
            }
            reader.readAsDataURL(file);
            return;
        }
        if (this.linkType === 'markdown') {
            reader.onload = async (e) => {
                this.checkedURL = e.target.result;
                if (this.cell) {
                    this.cell.source = this.checkedURL;
                }
                console.log(this.linkType)
                this.$update();
            }
            reader.readAsText(file);
            return;
        }
        reader.onload = async (e) => {
            let url = e.target.result;
            let res = await fetch(url);
            let blob = await res.blob();
            await this.saveAttachment(blob);
        }
        reader.readAsDataURL(file);
    }
    async deleteFile() {
        if (this.cell) {
            this.cell.url = this.cell.size = '';
            this.cell.attachments = false;
        }
        this.url = this.ext = this.linkType = this.checkedURL = '';
        this.$update();
    }
    async saveAttachment(blob, isEdited) {
        this.setLinkType(blob.type);
        let url = URL.createObjectURL(blob);
        this.checkedURL = url;
        if (this.cell && this.base?.fxSelected?._id) {
            this.cell.attachments = true;
            this.cell.url = isEdited ? 'edited attached image' : this.url;
            let _id = this.cell._idFile = 'file:' + this.base.fxSelected._id + '-' + (this.cell.ulid || FX.ulid());
            console.log(_id)
            let attachments = {
                _id,
                _attachments: {
                    'file': {
                        content_type: blob.type,
                        data: blob
                    }
                }
            }
            this.base.attachments ||= [];
            this.base.attachments.push(attachments);
        }
        this.$update();
    }
    openInNewTab(e) {
        let url = (this.pdfjsView ? this.iframeSrc : this.checkedURL) || '';
        window.open(url, '_blank').focus();
    }

    static styles = [$styles]
    
    render() {
        if (!this.checkedURL || !this.isReady) return null;
        return html`
            <div class="vertical flex w100 h100 relative overflow-y box ${this.cell?.docClass || ''}" style="${this.cell?.docStyle || ''}">
                ${(this.linkType === 'file' || !this.linkType || this.linkType === 'html') ? html`
                    <iframe .src=${this.iframeSrc || this.checkedURL} style="height: 100%; border: none; overflow: auto; min-height: 0px;"></iframe>
                ` : html``}
                ${this.linkType === 'image' ? html`
                    <div class="horizontal center box relative"></div>
                        <img class="box" src=${this.checkedURL} style="min-height: 0; height: 100%; width: 100%; object-fit: contain;">
                    </div>  
                ` : html``}
                ${this.linkType === 'video' ? html`
                    <div class="relative w100 h100">
                        <video src=${this.checkedURL} controls style="position: absolute; top: 25px; left: 0; width: 100%; height: calc(100% - 26px);"></video>
                    </div>
                ` : html``}
                ${this.linkType === 'audio' ? html`
                    <audio src=${this.checkedURL} controls style="padding: 4px; display: flex; margin: auto;"></audio>
                ` : html``}
                ${this.linkType === 'markdown'? html`
                    <fx-jmd class="block p10" .src=${this.cell?.source || this.checkedURL} style="position: absolute; top: 25px; left: 0; width: 100%; height: calc(100% - 26px);"></fx-jmd>
                ` : html``}
                ${this.linkType === 'epub' ? html`
                    <fx-jepub .source=${this.cell?.source} .url=${this.url} style="position: absolute; top: 25px; left: 0; width: 100%; height: calc(100% - 26px);"></fx-jepub>
                ` : html``}
            </div>
        `
    }
}

customElements.define('fx-jlink', FxJLink);
