import { css } from '/fx.js';

export const $styles = css`
 :host {
            display: block;
            position: relative;
            width: 100%;
            height: 100%;
            background: var(--fx-background);
            overflow: hidden;
            user-select: none;
        }
        .desk-container {
            position: relative;
            width: 100%;
            height: 100%;
            background-size: 100px 100px, 100px 100px, 10px 10px, 10px 10px;
            background-image:
                linear-gradient(rgba(200, 200, 200, 0.3) 1px, transparent 0),
                linear-gradient(90deg, rgba(200, 200, 200, 0.3) 1px, transparent 0),
                linear-gradient(rgba(200, 200, 200, 0.2) 1px, transparent 0),
                linear-gradient(90deg, rgba(200, 200, 200, 0.2) 1px, transparent 0);
            user-select: none;
        }
        .desk-item {
            position: absolute;
            border: 1px solid var(--item-border-color, #ccc);
            border-radius: 4px;
            background: var(--item-background, var(--fx-background));
            color: var(--item-text-color, var(--fx-color));
            cursor: move;
            display: flex;
            align-items: center;
            padding: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: box-shadow 0.2s ease;
        }
        /* .desk-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        .desk-item.selected {
            border-color: var(--fx-color-selected, #007acc);
            box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.3);
        } */
        .desk-item.dragging {
            z-index: 1000;
            opacity: 0.5;
            /* transform: rotate(2deg); */
        }
        .item-icon {
            margin-bottom: 4px;
        }
        .item-label {
            font-size: 12px;
            text-align: center;
            word-wrap: break-word;
            max-width: 100%;
            color: var(--item-text-color, var(--fx-color));
        }
        .resize-handle {
            position: absolute;
            background: var(--fx-color-selected, #007acc);
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        /* .desk-item.selected .resize-handle {
            opacity: 1;
        } */
        .resize-handle.nw { top: -4px; left: -4px; width: 8px; height: 8px; cursor: nw-resize; }
        .resize-handle.ne { top: -4px; right: -4px; width: 8px; height: 8px; cursor: ne-resize; }
        .resize-handle.sw { bottom: -4px; left: -4px; width: 8px; height: 8px; cursor: sw-resize; }
        .resize-handle.se { bottom: -4px; right: -4px; width: 8px; height: 8px; cursor: se-resize; }
        .resize-handle.n { top: -4px; left: 50%; transform: translateX(-50%); width: 8px; height: 8px; cursor: n-resize; }
        .resize-handle.s { bottom: -4px; left: 50%; transform: translateX(-50%); width: 8px; height: 8px; cursor: s-resize; }
        .resize-handle.w { top: 50%; left: -4px; transform: translateY(-50%); width: 8px; height: 8px; cursor: w-resize; }
        .resize-handle.e { top: 50%; right: -4px; transform: translateY(-50%); width: 8px; height: 8px; cursor: e-resize; }
        .drop-zone {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }
`

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;
// const usedIcons =
// {
// }
// FX.setIcons(usedIcons);
