import { FxElement, html, css } from '/fx.js';
import { $styles } from './desk.x.js';
import '../icon/icon.js';

export class FxDesk extends FxElement {
    static properties = {
        items: { type: Array, default: [], save: true },
        gridSize: { type: Number, default: 10 },
        snapToGrid: { type: Boolean, default: true },
        selectedItem: { type: Object, default: null },
        draggedItem: { type: Object, default: null },
        isDragging: { type: Boolean, default: false },
        isResizing: { type: Boolean, default: false },
        resizeHandle: { type: String, default: '' },
        dragOffset: { type: Object, default: { x: 0, y: 0 } },
        resizeStart: { type: Object, default: { x: 0, y: 0, width: 0, height: 0 } },
        minSize: { type: Number, default: 20 },
        itemW: { type: Number, default: 160 },
        itemH: { type: Number, default: 40 },
    }

    connectedCallback() {
        super.connectedCallback();
        this.style.setProperty('--grid-size', `${this.gridSize || 10}px`);
        document.addEventListener('pointermove', this.handlePointerMove.bind(this));
        document.addEventListener('pointerup', this.handlePointerUp.bind(this));
        this.addEventListener('drop', (e) => this.handleDrop(e));
        this.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.$listen('changed', (e) => {
            const v = e.get('value');
            if (v.type = 'moveTreeRow')
                this.handleDrop(v.originalEvent, v.draggedItem);
        })
    }
    disconnectedCallback() {
        super.disconnectedCallback();
        document.removeEventListener('pointermove', this.handlePointerMove.bind(this));
        document.removeEventListener('pointerup', this.handlePointerUp.bind(this));
    }

    itemClick(e, item, index) {
        e.preventDefault();
        e.stopPropagation();
        // this.fire('item-click', { item, index });
    }

    static styles = $styles

    renderDeskItem(item, index) {
        const isSelected = this.selectedItem === item;
        const isDragging = this.draggedItem === item;
        const style = `
            left: ${item.x || 0}px;
            top: ${item.y || 0}px;
            width: ${item.width || this.itemW}px;
            height: ${item.height || this.itemH}px;
            --item-background: ${item.backgroundColor || 'var(--fx-background)'};
            --item-border-color: ${item.borderColor || '#ccc'};
            --item-text-color: ${item.textColor || 'var(--fx-color)'};
        `
        return html`
            <div class="desk-item ${isSelected ? '' : ''} ${isDragging ? 'dragging' : ''}"
                 style="${style}"
                 data-index="${index}"
                 @pointerdown=${(e) => this.handleItemPointerDown(e, item, index)}
                 @click=${(e) => this.handleItemClick(e, item, index)}>
                ${item.icon ? html`
                    <fx-icon class="item-icon pointer mt4 mr4" url="${item.icon}" size="${item.iconSize || 48}" scale=1 @pointerdown=${e => this.itemClick(e, item)}></fx-icon>
                ` : ''}
                <div class="overflow-h w100 h100 flex relative" @contextmenu=${(e) => this.handleItemContextMenu(e, item, index)}>
                    <div class="item-label">${item.label || item.name || 'Item'}</div>
                </div>
                ${isSelected ? html`
                    <div class="resize-handle nw" @pointerdown=${(e) => this.handleResizeStart(e, 'nw')}></div>
                    <div class="resize-handle ne" @pointerdown=${(e) => this.handleResizeStart(e, 'ne')}></div>
                    <div class="resize-handle sw" @pointerdown=${(e) => this.handleResizeStart(e, 'sw')}></div>
                    <div class="resize-handle se" @pointerdown=${(e) => this.handleResizeStart(e, 'se')}></div>
                    <div class="resize-handle n" @pointerdown=${(e) => this.handleResizeStart(e, 'n')}></div>
                    <div class="resize-handle s" @pointerdown=${(e) => this.handleResizeStart(e, 's')}></div>
                    <div class="resize-handle w" @pointerdown=${(e) => this.handleResizeStart(e, 'w')}></div>
                    <div class="resize-handle e" @pointerdown=${(e) => this.handleResizeStart(e, 'e')}></div>
                ` : ''}
            </div>
        `
    }

    render() {
        return html`
            <div class="desk-container"
                 @click=${this.handleContainerClick}
                 @dragover=${this.handleDragOver}
                 @drop=${this.handleDrop}
                 @dragenter=${this.handleDragEnter}
                 @dragleave=${this.handleDragLeave}>
                <div class="drop-zone"></div>
                ${this.items.map((item, index) => this.renderDeskItem(item, index))}
            </div>
        `
    }

    handleItemContextMenu(e, item, index) {
        e.preventDefault();
        e.stopPropagation();
        this.isDragging = false;
        this.draggedItem = null;
        // this.fire('item-contextmenu', { item, event: e });
        this.showSettingsDialog(index);
    }
    async showSettingsDialog(index) {
        let item = this.items[index];
        const run = async (e, i) => {
            const id = i.id;
            if (id === 'sizeIcon') {
                item.iconSize = +e.target.value;
            }
            else if (id === 'iconName') {
                item.icon = e.target.value;
            }
            else if (id === 'selectIcon') {
                let res = await FX.show('dropdown', '/icons/icons.js', { isSelectIcon: true }, {}, 'fx', 'fx');
                if (res?.detail?.res) {
                    let spl = res.detail.res.split(':'),
                        icon = spl[0] + (spl[1] ? ':' + spl[1] : '');
                    item.icon = icon;
                }
            }
            else if (id === 'deleteIcon') {
                item.icon = '';
            }
            else if (id === 'deleteItem') {
                const res = await FX.showModal({ cancel: 'Cancel', ok: 'Ok', modal: '360, 160', label: 'Warning', info1: `Delete current item - ${item.label}?`, info2: `Удалить текущий элемент - ${item.label}?` })
                if (res.detail === 'ok') {
                    this.removeItem(item);
                }
            }
            this.items = [...this.items];
            this.$update();
        }
        const items = [
            {
                id: 'setsIcon', icon: 'flat-color-icons:stack-of-photos:28', label: 'icon', subLabel: 'действия с иконками', expanded: false, items: [
                    { id: 'iconName', icon: item.icon, label: 'icon name', subLabel: 'имя иконки', value: item.icon, _value: 'icon', run },
                    { id: 'selectIcon', icon: 'flat-color-icons:gallery:28', label: 'set icon', value: 'выбрать', is: 'button', run },
                    { id: 'sizeIcon', icon: 'flat-color-icons:edit-image:28', label: 'icon size', subLabel: 'размер иконки', value: item.iconSize, type: 'number', run },
                    { id: 'deleteIcon', icon: 'fx:close:28', fill: 'red', label: 'delete icon', value: 'удалить', is: 'button', run },
                ]
            },
            {
                id: 'deleteItem', icon: 'fx:close:28', label: 'delete item', subLabel: 'удалить элемент', fill: 'red', value: 'delete', is: 'button' , run
            }
        ]
        await FX.showGrid({
            id: this.id + '-item-settings',
            item: items,
            rowHeight: 32,
            hideSelected: true
        }, {
            parent: this.$qs('#add'),
            label: 'Settings',
            intersect: true,
            align: 'left',
            class: 'br',
            minWidth: 280,
            id: this.id + '-create-local-db',
            draggable: true,
            resizable: false,
            btnCloseOnly: false
        })
    }

    handleContainerClick(e) {
        if (e.target.classList.contains('desk-container') || e.target.classList.contains('drop-zone')) {
            this.selectedItem = null;
            this.$update();
        }
    }
    handleItemClick(e, item) {
        e.stopPropagation();
        this.selectedItem = item;
        this.$update();
        this.fire('item-selected', { item });
    }

    handleItemPointerDown(e, item, index) {
        if (e.target.classList.contains('resize-handle')) return;
        e.preventDefault();
        this.isDragging = true;
        this.draggedItem = item;
        this.selectedItem = item;
        const rect = e.currentTarget.getBoundingClientRect();
        const containerRect = this.getBoundingClientRect();
        this.dragOffset = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        }
        this.$update();
    }
    handlePointerMove(e) {
        if (this.isDragging && this.draggedItem) {
            this.handleDragMove(e);
        } else if (this.isResizing && this.selectedItem) {
            this.handleResizeMove(e);
        }
    }
    handleDragMove(e) {
        const containerRect = this.getBoundingClientRect();
        let newX = e.clientX - containerRect.left - this.dragOffset.x;
        let newY = e.clientY - containerRect.top - this.dragOffset.y;
        if (this.snapToGrid) {
            newX = Math.round(newX / this.gridSize) * this.gridSize;
            newY = Math.round(newY / this.gridSize) * this.gridSize;
        }
        newX = Math.max(0, Math.min(newX, containerRect.width - (this.draggedItem.width || this.itemW)));
        newY = Math.max(0, Math.min(newY, containerRect.height - (this.draggedItem.height || this.itemH)));
        this.draggedItem.x = newX;
        this.draggedItem.y = newY
        this.$update();
    }
    handlePointerUp(e) {
        if (this.isDragging) {
            this.isDragging = false;
            this.draggedItem = null;
            this.items = [...this.items];
            this.$update();
            this.fire('item-moved', { items: this.items });
        }
        if (this.isResizing) {
            this.isResizing = false;
            this.resizeHandle = '';
            this.items = [...this.items];
            this.$update();
            this.fire('item-resized', { items: this.items });
        }
    }
    handleResizeStart(e, handle) {
        e.preventDefault();
        e.stopPropagation();
        this.isResizing = true;
        this.resizeHandle = handle;
        this.resizeStart = {
            x: e.clientX,
            y: e.clientY,
            width: this.selectedItem.width || this.itemW,
            height: this.selectedItem.height || this.itemH,
            itemX: this.selectedItem.x || 0,
            itemY: this.selectedItem.y || 0
        }
    }
    handleResizeMove(e) {
        if (!this.isResizing || !this.selectedItem) return;
        const deltaX = e.clientX - this.resizeStart.x;
        const deltaY = e.clientY - this.resizeStart.y;
        let newWidth = this.resizeStart.width;
        let newHeight = this.resizeStart.height;
        let newX = this.resizeStart.itemX;
        let newY = this.resizeStart.itemY;
        switch (this.resizeHandle) {
            case 'se':
                newWidth = Math.max(this.minSize, this.resizeStart.width + deltaX);
                newHeight = Math.max(this.minSize, this.resizeStart.height + deltaY);
                break;
            case 'sw':
                newWidth = Math.max(this.minSize, this.resizeStart.width - deltaX);
                newHeight = Math.max(this.minSize, this.resizeStart.height + deltaY);
                newX = this.resizeStart.itemX + deltaX;
                break;
            case 'ne':
                newWidth = Math.max(this.minSize, this.resizeStart.width + deltaX);
                newHeight = Math.max(this.minSize, this.resizeStart.height - deltaY);
                newY = this.resizeStart.itemY + deltaY;
                break;
            case 'nw':
                newWidth = Math.max(this.minSize, this.resizeStart.width - deltaX);
                newHeight = Math.max(this.minSize, this.resizeStart.height - deltaY);
                newX = this.resizeStart.itemX + deltaX;
                newY = this.resizeStart.itemY + deltaY;
                break;
            case 'n':
                newHeight = Math.max(this.minSize, this.resizeStart.height - deltaY);
                newY = this.resizeStart.itemY + deltaY;
                break;
            case 's':
                newHeight = Math.max(this.minSize, this.resizeStart.height + deltaY);
                break;
            case 'w':
                newWidth = Math.max(this.minSize, this.resizeStart.width - deltaX);
                newX = this.resizeStart.itemX + deltaX;
                break;
            case 'e':
                newWidth = Math.max(this.minSize, this.resizeStart.width + deltaX);
                break;
        }
        if (this.snapToGrid) {
            newWidth = Math.round(newWidth / this.gridSize) * this.gridSize;
            newHeight = Math.round(newHeight / this.gridSize) * this.gridSize;
            newX = Math.round(newX / this.gridSize) * this.gridSize;
            newY = Math.round(newY / this.gridSize) * this.gridSize;
        }
        this.selectedItem.width = newWidth;
        this.selectedItem.height = newHeight;
        this.selectedItem.x = newX;
        this.selectedItem.y = newY;
        this.$update();
    }
    handleDragEnter(e) {
        e.preventDefault();
        const dropZone = this.shadowRoot.querySelector('.drop-zone');
        if (dropZone) dropZone.classList.add('drag-over');
    }
    handleDragLeave(e) {
        if (!this.contains(e.relatedTarget)) {
            const dropZone = this.shadowRoot.querySelector('.drop-zone');
            if (dropZone) dropZone.classList.remove('drag-over');
        }
    }
    handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'copy';
    }
    handleDrop(e, data) {
        e.preventDefault();
        e.stopPropagation();
        const dropZone = this.shadowRoot.querySelector('.drop-zone');
        if (dropZone) dropZone.classList.remove('drag-over');
        if (!data)
            data = { label: e.dataTransfer.getData('text/plain') };
        if (!data) return;
        data = { ...data };
        const containerRect = this.getBoundingClientRect();
        let x = e.clientX - containerRect.left - this.itemH;
        let y = e.clientY - containerRect.top - 30;
        if (this.snapToGrid) {
            x = Math.round(x / this.gridSize) * this.gridSize;
            y = Math.round(y / this.gridSize) * this.gridSize;
        }
        data.x = x;
        data.y = y;
        this.addItem(data);
        this.async(() => {
            this.fire('desk-item-added', { item: data });
        })
    }

    addItem(itemData) {
        const newItem = {
            id: itemData.id || `item_${Date.now()}`,
            label: itemData.label || itemData.name || '',
            icon: itemData.icon || '',
            x: itemData.x || 0,
            y: itemData.y || 0,
            width: itemData.width || this.itemW,
            height: itemData.height || this.itemH,
            backgroundColor: itemData.backgroundColor || 'var(--fx-background)',
            borderColor: itemData.borderColor || '#ccc',
            textColor: itemData.textColor || 'var(--fx-color)',
            iconSize: itemData.iconSize || 36,
            // ...itemData
        }
        this.items = [...this.items, newItem];
        this.$update();
        return newItem;
    }
    removeItem(item) {
        this.items = this.items.filter(i => i !== item);
        if (this.selectedItem === item) {
            this.selectedItem = null;
        }
        this.$update();
    }
    clearDesk() {
        this.items = [];
        this.selectedItem = null;
        this.$update();
    }
    getItems() {
        return this.items;
    }
    setGridSize(size) {
        this.gridSize = size;
        this.style.setProperty('--grid-size', `${size}px`);
        this.$update();
    }
}

customElements.define('fx-desk', FxDesk);
