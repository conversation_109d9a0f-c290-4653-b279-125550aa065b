<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FX Desk Demo - Интеграция с Tree и Table</title>
    <script type="module" src="/fx.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--fx-background, #f5f5f5);
            color: var(--fx-color, #333);
        }
        
        .demo-container {
            display: grid;
            grid-template-columns: 250px 250px 1fr;
            grid-template-rows: 60px 1fr;
            height: 100vh;
            gap: 1px;
            background: var(--fx-border-color, #ddd);
        }
        
        .header {
            grid-column: 1 / -1;
            background: var(--fx-header-background, #fff);
            padding: 16px;
            border-bottom: 1px solid var(--fx-border-color, #ddd);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .tree-panel, .table-panel {
            background: var(--fx-panel-background, #fff);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .desk-panel {
            background: var(--fx-background, #f9f9f9);
            position: relative;
        }
        
        .panel-header {
            padding: 12px 16px;
            background: var(--fx-panel-top-background, #f0f0f0);
            border-bottom: 1px solid var(--fx-border-color, #ddd);
            font-weight: 500;
            font-size: 14px;
        }
        
        .panel-content {
            flex: 1;
            overflow: hidden;
        }
        
        .btn {
            background: var(--fx-color-selected, #007acc);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 8px;
            font-size: 12px;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .btn.secondary {
            background: var(--fx-border-color, #999);
        }
        
        .info-panel {
            position: absolute;
            top: 16px;
            right: 16px;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            max-width: 300px;
            font-size: 12px;
            z-index: 100;
        }
        
        .selected-info {
            position: absolute;
            bottom: 16px;
            left: 16px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="header">
            <h2>FX Desk Demo - Рабочий стол с интеграцией</h2>
            <div>
                <button class="btn" onclick="exportConfig()">Экспорт</button>
                <button class="btn" onclick="importConfig()">Импорт</button>
                <button class="btn secondary" onclick="clearDesk()">Очистить</button>
            </div>
        </div>
        
        <div class="tree-panel">
            <div class="panel-header">Дерево элементов</div>
            <div class="panel-content">
                <fx-tree id="demoTree"></fx-tree>
            </div>
        </div>
        
        <div class="table-panel">
            <div class="panel-header">Таблица данных</div>
            <div class="panel-content">
                <fx-table id="demoTable"></fx-table>
            </div>
        </div>
        
        <div class="desk-panel">
            <fx-desk id="demoDesk"></fx-desk>
            
            <div class="info-panel">
                <strong>Инструкция:</strong><br>
                • Перетащите элементы из дерева или таблицы на рабочий стол<br>
                • Кликните на элемент для выделения<br>
                • Перетаскивайте и изменяйте размер элементов<br>
                • Используйте кнопки для управления
            </div>
            
            <div class="selected-info" id="selectedInfo" style="display: none;">
                Выбран элемент: <span id="selectedLabel"></span>
            </div>
        </div>
    </div>

    <script type="module">
        import './desk.js';
        import { DeskIntegration } from './desk-integration.js';
        import '../tree/tree.js';
        import '../table/table.js';
        
        const desk = document.getElementById('demoDesk');
        const tree = document.getElementById('demoTree');
        const table = document.getElementById('demoTable');
        const selectedInfo = document.getElementById('selectedInfo');
        const selectedLabel = document.getElementById('selectedLabel');
        
        // Инициализация интеграции
        const integration = new DeskIntegration(desk);
        
        // Настройка дерева
        tree.item = {
            id: 'root',
            label: 'Корневая папка',
            icon: 'mdi:folder',
            expanded: true,
            items: [
                {
                    id: 'documents',
                    label: 'Документы',
                    icon: 'mdi:folder-text',
                    type: 'folder',
                    expanded: true,
                    items: [
                        { id: 'doc1', label: 'Отчет.docx', icon: 'mdi:file-word', type: 'document' },
                        { id: 'doc2', label: 'Презентация.pptx', icon: 'mdi:file-powerpoint', type: 'document' },
                        { id: 'doc3', label: 'Таблица.xlsx', icon: 'mdi:file-excel', type: 'document' }
                    ]
                },
                {
                    id: 'projects',
                    label: 'Проекты',
                    icon: 'mdi:folder-cog',
                    type: 'folder',
                    items: [
                        { id: 'proj1', label: 'Проект А', icon: 'mdi:rocket', type: 'project' },
                        { id: 'proj2', label: 'Проект Б', icon: 'mdi:star', type: 'project' }
                    ]
                },
                {
                    id: 'contacts',
                    label: 'Контакты',
                    icon: 'mdi:account-group',
                    type: 'folder',
                    items: [
                        { id: 'person1', label: 'Иван Петров', icon: 'mdi:account', type: 'person' },
                        { id: 'person2', label: 'Мария Сидорова', icon: 'mdi:account', type: 'person' },
                        { id: 'person3', label: 'Алексей Козлов', icon: 'mdi:account', type: 'person' }
                    ]
                }
            ]
        };
        
        // Настройка таблицы
        table.columns = [
            { field: 'name', header: 'Имя', width: 120 },
            { field: 'type', header: 'Тип', width: 80 },
            { field: 'status', header: 'Статус', width: 80 }
        ];
        
        table.data = [
            { id: 'task1', name: 'Задача 1', type: 'task', status: 'Активна', icon: 'mdi:check-circle' },
            { id: 'task2', name: 'Задача 2', type: 'task', status: 'Завершена', icon: 'mdi:check-circle' },
            { id: 'note1', name: 'Заметка 1', type: 'note', status: 'Новая', icon: 'mdi:note-text' },
            { id: 'note2', name: 'Заметка 2', type: 'note', status: 'Архив', icon: 'mdi:note-text' },
            { id: 'link1', name: 'Ссылка 1', type: 'link', status: 'Активна', icon: 'mdi:link' },
            { id: 'link2', name: 'Ссылка 2', type: 'link', status: 'Проверить', icon: 'mdi:link' }
        ];
        
        // Настройка drag and drop для дерева
        let draggedTreeItem = null;
        let isDraggingFromTree = false;

        // Добавляем обработчик на весь document для отлова всех dragstart
        document.addEventListener('dragstart', (e) => {
            // Если drag начался из дерева
            if (e.target.closest('fx-tree')) {
                if (tree.selected) {
                    draggedTreeItem = tree.selected;
                    isDraggingFromTree = true;
                    console.log('Начинаем drag из дерева:', draggedTreeItem.label);
                }
            }

            // Если drag начался из таблицы
            if (e.target.closest('fx-table')) {
                if (table.selectedRowIndex >= 0 && table.data[table.selectedRowIndex]) {
                    draggedTableRow = table.data[table.selectedRowIndex];
                    isDraggingFromTable = true;
                    console.log('Начинаем drag из таблицы:', draggedTableRow.name);
                }
            }
        });

        // Общий обработчик dragend для всех элементов
        document.addEventListener('dragend', (e) => {
            setTimeout(() => {
                // Обработка drag из дерева
                if (isDraggingFromTree && draggedTreeItem) {
                    console.log('Добавляем элемент из дерева:', draggedTreeItem.label);
                    addItemToDesk(draggedTreeItem, lastMousePosition.x, lastMousePosition.y);
                    draggedTreeItem = null;
                    isDraggingFromTree = false;
                }

                // Обработка drag из таблицы
                if (isDraggingFromTable && draggedTableRow) {
                    console.log('Добавляем элемент из таблицы:', draggedTableRow.name);
                    addItemToDesk(draggedTableRow, lastMousePosition.x, lastMousePosition.y);
                    draggedTableRow = null;
                    isDraggingFromTable = false;
                }
            }, 50);
        });

        // Настройка drag and drop для таблицы
        let draggedTableRow = null;
        let isDraggingFromTable = false;

        // Делаем строки таблицы перетаскиваемыми при клике
        table.addEventListener('click', (e) => {
            const row = e.target.closest('tr');
            if (row && !row.classList.contains('header-row')) {
                // Убираем draggable у всех строк
                const allRows = table.shadowRoot.querySelectorAll('tr');
                allRows.forEach(r => r.draggable = false);

                // Делаем текущую строку draggable
                row.draggable = true;

                const rowIndex = Array.from(row.parentNode.children).indexOf(row) - 1;
                if (rowIndex >= 0 && table.data[rowIndex]) {
                    table.selectedRowIndex = rowIndex;
                    console.log('Строка готова к перетаскиванию:', table.data[rowIndex].name);
                }
            }
        });

        // Функция для добавления элемента на desk
        function addItemToDesk(itemData, x, y) {
            if (!itemData) {
                console.error('Нет данных для добавления элемента!');
                return;
            }

            const deskItem = {
                id: itemData.id || `item_${Date.now()}`,
                label: itemData.label || itemData.name || 'Новый элемент',
                icon: itemData.icon || 'mdi:file',
                x: x || 100,
                y: y || 100,
                width: 140,
                height: 90,
                backgroundColor: getItemColor(itemData),
                borderColor: getItemBorderColor(itemData),
                textColor: 'var(--fx-color)',
                originalData: itemData
            };

            desk.addItem(deskItem);
            console.log('✅ Добавлен элемент:', deskItem.label);
        }

        // Обработчик клика по desk для определения позиции drop
        let lastMousePosition = { x: 100, y: 100 };

        desk.addEventListener('mousemove', (e) => {
            const rect = desk.getBoundingClientRect();
            lastMousePosition.x = e.clientX - rect.left;
            lastMousePosition.y = e.clientY - rect.top;
        });

        // Глобальный обработчик drop для отладки
        document.addEventListener('drop', (e) => {
            console.log('Глобальный drop на document:', e);

            // Проверяем, попал ли drop на наш desk
            if (e.target.closest('fx-desk')) {
                console.log('Drop попал на fx-desk!');
                e.preventDefault();

                if (isDraggingFromTree && draggedTreeItem) {
                    console.log('Добавляем элемент из дерева через drop');
                    addItemToDesk(draggedTreeItem, lastMousePosition.x, lastMousePosition.y);
                    draggedTreeItem = null;
                    isDraggingFromTree = false;
                } else if (isDraggingFromTable && draggedTableRow) {
                    console.log('Добавляем элемент из таблицы через drop');
                    addItemToDesk(draggedTableRow, lastMousePosition.x, lastMousePosition.y);
                    draggedTableRow = null;
                    isDraggingFromTable = false;
                }
            }
        });

        document.addEventListener('dragover', (e) => {
            if (e.target.closest('fx-desk')) {
                e.preventDefault();
            }
        });

        // Обработка событий рабочего стола
        desk.addEventListener('external-drop', (e) => {
            try {
                const itemData = JSON.parse(e.detail.data);
                console.log('Получены данные для drop:', itemData);

                // Создаем элемент рабочего стола на основе данных
                const deskItem = {
                    id: itemData.id || `item_${Date.now()}`,
                    label: itemData.label || itemData.name || 'Новый элемент',
                    icon: itemData.icon || 'mdi:file',
                    x: e.detail.x,
                    y: e.detail.y,
                    width: 140,
                    height: 90,
                    backgroundColor: getItemColor(itemData),
                    borderColor: getItemBorderColor(itemData),
                    textColor: 'var(--fx-color)',
                    originalData: itemData
                };

                desk.addItem(deskItem);
                console.log('Элемент добавлен на рабочий стол:', deskItem);
            } catch (err) {
                console.error('Ошибка при добавлении элемента:', err);
                console.log('Данные:', e.detail.data);
            }
        });

        // Функции для определения цветов элементов
        function getItemColor(item) {
            const colorMap = {
                'document': '#e3f2fd',
                'folder': '#fff3e0',
                'person': '#f3e5f5',
                'project': '#e8f5e8',
                'task': '#fff8e1',
                'note': '#fce4ec',
                'link': '#f1f8e9'
            };
            return colorMap[item.type] || '#f5f5f5';
        }

        function getItemBorderColor(item) {
            const borderMap = {
                'document': '#2196f3',
                'folder': '#ff9800',
                'person': '#9c27b0',
                'project': '#4caf50',
                'task': '#ffc107',
                'note': '#e91e63',
                'link': '#8bc34a'
            };
            return borderMap[item.type] || '#ccc';
        }

        // Обработка выбора элементов на рабочем столе
        desk.addEventListener('item-selected', (e) => {
            const item = e.detail.item;
            selectedLabel.textContent = item.label || 'Без названия';
            selectedInfo.style.display = 'block';
        });
        
        // Скрытие информации при клике на пустое место
        desk.addEventListener('click', (e) => {
            if (!desk.selectedItem) {
                selectedInfo.style.display = 'none';
            }
        });
        
        // Глобальные функции
        window.clearDesk = () => {
            desk.clearDesk();
            selectedInfo.style.display = 'none';
        };
        
        window.exportConfig = () => {
            const config = integration.exportDeskConfig();
            const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'desk-config.json';
            a.click();
            URL.revokeObjectURL(url);
        };
        
        window.importConfig = () => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const config = JSON.parse(e.target.result);
                            integration.importDeskConfig(config);
                        } catch (error) {
                            alert('Ошибка при импорте конфигурации: ' + error.message);
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        };
        
        // Добавляем примеры элементов при загрузке
        setTimeout(() => {
            desk.addItem({
                id: 'welcome',
                label: 'Добро пожаловать!',
                icon: 'mdi:hand-wave',
                backgroundColor: '#e8f5e8',
                borderColor: '#4caf50',
                x: 50,
                y: 50,
                width: 160,
                height: 100
            });
            
            desk.addItem({
                id: 'demo',
                label: 'Demo элемент',
                icon: 'mdi:star',
                backgroundColor: '#fff3e0',
                borderColor: '#ff9800',
                x: 250,
                y: 80,
                width: 140,
                height: 90
            });
        }, 100);
    </script>
</body>
</html>
