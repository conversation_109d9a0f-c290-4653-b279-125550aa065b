// Интеграция fx-desk с fx-tree, fx-table и другими компонентами

export class DeskIntegration {
    constructor(desk) {
        this.desk = desk;
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Обработка drop событий от fx-tree
        this.desk.addEventListener('external-drop', this.handleExternalDrop.bind(this));
        
        // Обработка событий рабочего стола
        this.desk.addEventListener('item-selected', this.handleItemSelected.bind(this));
        this.desk.addEventListener('item-moved', this.handleItemMoved.bind(this));
        this.desk.addEventListener('item-resized', this.handleItemResized.bind(this));
    }

    // Обработка drop из внешних источников
    handleExternalDrop(e) {
        const { data, x, y, originalEvent } = e.detail;
        
        try {
            // Пытаемся распарсить JSON данные
            let itemData;
            try {
                itemData = JSON.parse(data);
            } catch {
                // Если не JSON, создаем базовый элемент
                itemData = { label: data || 'Новый элемент' };
            }

            // Определяем источник drop
            const sourceElement = originalEvent.target.closest('fx-tree, fx-table');
            
            if (sourceElement) {
                if (sourceElement.tagName.toLowerCase() === 'fx-tree') {
                    this.handleTreeDrop(itemData, x, y, sourceElement);
                } else if (sourceElement.tagName.toLowerCase() === 'fx-table') {
                    this.handleTableDrop(itemData, x, y, sourceElement);
                }
            } else {
                // Обычный drop
                this.createDeskItem(itemData, x, y);
            }
        } catch (error) {
            console.error('Ошибка при обработке drop:', error);
        }
    }

    // Обработка drop из fx-tree
    handleTreeDrop(itemData, x, y, treeElement) {
        const treeItem = this.extractTreeItemData(itemData);
        
        const deskItem = {
            ...treeItem,
            x,
            y,
            source: 'tree',
            sourceId: treeElement.id,
            originalData: itemData
        };

        this.createDeskItem(deskItem, x, y);
    }

    // Обработка drop из fx-table
    handleTableDrop(itemData, x, y, tableElement) {
        const tableItem = this.extractTableItemData(itemData);
        
        const deskItem = {
            ...tableItem,
            x,
            y,
            source: 'table',
            sourceId: tableElement.id,
            originalData: itemData
        };

        this.createDeskItem(deskItem, x, y);
    }

    // Извлечение данных из элемента дерева
    extractTreeItemData(item) {
        return {
            id: item.id || item._id,
            label: item.label || item.name || 'Tree Item',
            icon: item.icon || 'mdi:file-tree',
            backgroundColor: this.getItemColor(item, 'tree'),
            borderColor: this.getItemBorderColor(item, 'tree'),
            textColor: item.textColor || 'var(--fx-color)',
            width: 140,
            height: 90,
            type: item.type || 'tree-item'
        };
    }

    // Извлечение данных из элемента таблицы
    extractTableItemData(item) {
        return {
            id: item.id || item._id,
            label: item.name || item.label || item.title || 'Table Item',
            icon: item.icon || 'mdi:table-row',
            backgroundColor: this.getItemColor(item, 'table'),
            borderColor: this.getItemBorderColor(item, 'table'),
            textColor: item.textColor || 'var(--fx-color)',
            width: 160,
            height: 100,
            type: item.type || 'table-item'
        };
    }

    // Определение цвета элемента по типу
    getItemColor(item, source) {
        if (item.backgroundColor) return item.backgroundColor;
        
        const colorMap = {
            tree: {
                'item': '#e8f5e8',
                'folder': '#fff3e0',
                'person': '#f3e5f5',
                'document': '#e3f2fd',
                'default': '#f5f5f5'
            },
            table: {
                'person': '#f3e5f5',
                'contact': '#e3f2fd',
                'record': '#fff8e1',
                'default': '#f0f0f0'
            }
        };

        const sourceColors = colorMap[source] || colorMap.tree;
        return sourceColors[item.type] || sourceColors.default;
    }

    // Определение цвета границы элемента
    getItemBorderColor(item, source) {
        if (item.borderColor) return item.borderColor;
        
        const borderMap = {
            tree: {
                'item': '#4caf50',
                'folder': '#ff9800',
                'person': '#9c27b0',
                'document': '#2196f3',
                'default': '#ccc'
            },
            table: {
                'person': '#9c27b0',
                'contact': '#2196f3',
                'record': '#ffc107',
                'default': '#999'
            }
        };

        const sourceBorders = borderMap[source] || borderMap.tree;
        return sourceBorders[item.type] || sourceBorders.default;
    }

    // Создание элемента на рабочем столе
    createDeskItem(itemData, x, y) {
        const finalItem = {
            x,
            y,
            ...itemData
        };

        return this.desk.addItem(finalItem);
    }

    // Обработка выбора элемента
    handleItemSelected(e) {
        const item = e.detail.item;
        
        // Уведомляем другие компоненты о выборе
        this.notifySelection(item);
        
        // Можно добавить дополнительную логику
        console.log('Выбран элемент на рабочем столе:', item);
    }

    // Обработка перемещения элементов
    handleItemMoved(e) {
        const items = e.detail.items;
        
        // Сохраняем позиции элементов
        this.saveItemPositions(items);
        
        console.log('Элементы перемещены');
    }

    // Обработка изменения размера элементов
    handleItemResized(e) {
        const items = e.detail.items;
        
        // Сохраняем размеры элементов
        this.saveItemSizes(items);
        
        console.log('Размеры элементов изменены');
    }

    // Уведомление других компонентов о выборе
    notifySelection(item) {
        // Создаем кастомное событие для уведомления
        const event = new CustomEvent('desk-item-selected', {
            detail: { item },
            bubbles: true
        });
        
        this.desk.dispatchEvent(event);
    }

    // Сохранение позиций элементов (можно расширить для сохранения в localStorage или базу данных)
    saveItemPositions(items) {
        const positions = items.map(item => ({
            id: item.id,
            x: item.x,
            y: item.y
        }));
        
        // Сохраняем в localStorage
        localStorage.setItem('desk-positions', JSON.stringify(positions));
    }

    // Сохранение размеров элементов
    saveItemSizes(items) {
        const sizes = items.map(item => ({
            id: item.id,
            width: item.width,
            height: item.height
        }));
        
        // Сохраняем в localStorage
        localStorage.setItem('desk-sizes', JSON.stringify(sizes));
    }

    // Загрузка сохраненных позиций
    loadItemPositions() {
        try {
            const positions = JSON.parse(localStorage.getItem('desk-positions') || '[]');
            return positions;
        } catch {
            return [];
        }
    }

    // Загрузка сохраненных размеров
    loadItemSizes() {
        try {
            const sizes = JSON.parse(localStorage.getItem('desk-sizes') || '[]');
            return sizes;
        } catch {
            return [];
        }
    }

    // Применение сохраненных настроек к элементам
    applySavedSettings() {
        const positions = this.loadItemPositions();
        const sizes = this.loadItemSizes();
        
        this.desk.items.forEach(item => {
            // Применяем сохраненные позиции
            const savedPosition = positions.find(p => p.id === item.id);
            if (savedPosition) {
                item.x = savedPosition.x;
                item.y = savedPosition.y;
            }
            
            // Применяем сохраненные размеры
            const savedSize = sizes.find(s => s.id === item.id);
            if (savedSize) {
                item.width = savedSize.width;
                item.height = savedSize.height;
            }
        });
        
        this.desk.$update();
    }

    // Экспорт конфигурации рабочего стола
    exportDeskConfig() {
        return {
            items: this.desk.items,
            gridSize: this.desk.gridSize,
            snapToGrid: this.desk.snapToGrid,
            timestamp: new Date().toISOString()
        };
    }

    // Импорт конфигурации рабочего стола
    importDeskConfig(config) {
        if (config.items) {
            this.desk.items = config.items;
        }
        
        if (config.gridSize) {
            this.desk.setGridSize(config.gridSize);
        }
        
        if (typeof config.snapToGrid === 'boolean') {
            this.desk.snapToGrid = config.snapToGrid;
        }
        
        this.desk.$update();
    }

    // Поиск элементов на рабочем столе
    findItems(query) {
        const lowerQuery = query.toLowerCase();
        return this.desk.items.filter(item => 
            (item.label && item.label.toLowerCase().includes(lowerQuery)) ||
            (item.type && item.type.toLowerCase().includes(lowerQuery)) ||
            (item.id && item.id.toLowerCase().includes(lowerQuery))
        );
    }

    // Группировка элементов
    groupItems(items, spacing = 20) {
        if (items.length < 2) return;
        
        // Находим центр группы
        const centerX = items.reduce((sum, item) => sum + item.x, 0) / items.length;
        const centerY = items.reduce((sum, item) => sum + item.y, 0) / items.length;
        
        // Располагаем элементы в сетке вокруг центра
        const cols = Math.ceil(Math.sqrt(items.length));
        
        items.forEach((item, index) => {
            const row = Math.floor(index / cols);
            const col = index % cols;
            
            item.x = centerX + (col - cols / 2) * (item.width + spacing);
            item.y = centerY + (row - Math.floor(items.length / cols) / 2) * (item.height + spacing);
        });
        
        this.desk.$update();
    }

    // Выравнивание элементов
    alignItems(items, direction = 'horizontal') {
        if (items.length < 2) return;
        
        if (direction === 'horizontal') {
            const avgY = items.reduce((sum, item) => sum + item.y, 0) / items.length;
            items.forEach(item => item.y = avgY);
        } else {
            const avgX = items.reduce((sum, item) => sum + item.x, 0) / items.length;
            items.forEach(item => item.x = avgX);
        }
        
        this.desk.$update();
    }
}
