<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FX Desk - Рабочий стол</title>
    <script type="module" src="/fx.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--fx-background, #f5f5f5);
            color: var(--fx-color, #333);
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 300px;
            background: var(--fx-panel-background, #fff);
            border-right: 1px solid var(--fx-border-color, #ddd);
            padding: 16px;
            overflow-y: auto;
        }
        
        .desk-area {
            flex: 1;
            position: relative;
        }
        
        .controls {
            margin-bottom: 20px;
        }
        
        .control-group {
            margin-bottom: 16px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
        }
        
        .control-group input, .control-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--fx-border-color, #ddd);
            border-radius: 4px;
            background: var(--fx-background, #fff);
            color: var(--fx-color, #333);
        }
        
        .btn {
            background: var(--fx-color-selected, #007acc);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .sample-items {
            border: 1px solid var(--fx-border-color, #ddd);
            border-radius: 4px;
            padding: 12px;
        }
        
        .sample-item {
            display: flex;
            align-items: center;
            padding: 8px;
            margin-bottom: 4px;
            background: var(--fx-background, #f9f9f9);
            border: 1px solid var(--fx-border-color, #eee);
            border-radius: 4px;
            cursor: grab;
        }
        
        .sample-item:hover {
            background: var(--fx-panel-background, #f0f0f0);
        }
        
        .sample-item fx-icon {
            margin-right: 8px;
        }
        
        .info {
            margin-top: 20px;
            padding: 12px;
            background: var(--fx-panel-background, #f9f9f9);
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h3>Управление рабочим столом</h3>
            
            <div class="controls">
                <div class="control-group">
                    <label>Размер сетки:</label>
                    <input type="range" id="gridSize" min="10" max="50" value="20">
                    <span id="gridSizeValue">20px</span>
                </div>
                
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="snapToGrid" checked> 
                        Привязка к сетке
                    </label>
                </div>
                
                <button class="btn" onclick="clearDesk()">Очистить стол</button>
                <button class="btn" onclick="addRandomItem()">Добавить элемент</button>
            </div>
            
            <h4>Примеры элементов</h4>
            <div class="sample-items">
                <div class="sample-item" draggable="true" data-item='{"label":"Документ","icon":"mdi:file-document","backgroundColor":"#e3f2fd","borderColor":"#2196f3"}'>
                    <fx-icon url="mdi:file-document" scale="1.2"></fx-icon>
                    Документ
                </div>
                
                <div class="sample-item" draggable="true" data-item='{"label":"Папка","icon":"mdi:folder","backgroundColor":"#fff3e0","borderColor":"#ff9800"}'>
                    <fx-icon url="mdi:folder" scale="1.2"></fx-icon>
                    Папка
                </div>
                
                <div class="sample-item" draggable="true" data-item='{"label":"Персона","icon":"mdi:account","backgroundColor":"#f3e5f5","borderColor":"#9c27b0"}'>
                    <fx-icon url="mdi:account" scale="1.2"></fx-icon>
                    Персона
                </div>
                
                <div class="sample-item" draggable="true" data-item='{"label":"Задача","icon":"mdi:check-circle","backgroundColor":"#e8f5e8","borderColor":"#4caf50"}'>
                    <fx-icon url="mdi:check-circle" scale="1.2"></fx-icon>
                    Задача
                </div>
                
                <div class="sample-item" draggable="true" data-item='{"label":"Заметка","icon":"mdi:note-text","backgroundColor":"#fff8e1","borderColor":"#ffc107"}'>
                    <fx-icon url="mdi:note-text" scale="1.2"></fx-icon>
                    Заметка
                </div>
                
                <div class="sample-item" draggable="true" data-item='{"label":"Ссылка","icon":"mdi:link","backgroundColor":"#fce4ec","borderColor":"#e91e63"}'>
                    <fx-icon url="mdi:link" scale="1.2"></fx-icon>
                    Ссылка
                </div>
            </div>
            
            <div class="info">
                <strong>Инструкция:</strong><br>
                • Перетащите элементы из списка на рабочий стол<br>
                • Кликните на элемент для выделения<br>
                • Перетаскивайте элементы по столу<br>
                • Изменяйте размер выделенного элемента за углы<br>
                • Элементы привязываются к сетке
            </div>
        </div>
        
        <div class="desk-area">
            <fx-desk id="desk"></fx-desk>
        </div>
    </div>

    <script type="module">
        import './desk.js';
        
        const desk = document.getElementById('desk');
        const gridSizeSlider = document.getElementById('gridSize');
        const gridSizeValue = document.getElementById('gridSizeValue');
        const snapToGridCheckbox = document.getElementById('snapToGrid');
        
        // Настройка контролов
        gridSizeSlider.addEventListener('input', (e) => {
            const size = parseInt(e.target.value);
            desk.setGridSize(size);
            gridSizeValue.textContent = size + 'px';
        });
        
        snapToGridCheckbox.addEventListener('change', (e) => {
            desk.snapToGrid = e.target.checked;
        });
        
        // Обработка drag and drop из боковой панели
        document.querySelectorAll('.sample-item').forEach(item => {
            item.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', item.dataset.item);
            });
        });
        
        // Обработка событий рабочего стола
        desk.addEventListener('external-drop', (e) => {
            try {
                const itemData = JSON.parse(e.detail.data);
                itemData.x = e.detail.x;
                itemData.y = e.detail.y;
                desk.addItem(itemData);
            } catch (err) {
                console.error('Ошибка при добавлении элемента:', err);
            }
        });
        
        desk.addEventListener('item-selected', (e) => {
            console.log('Выбран элемент:', e.detail.item);
        });
        
        desk.addEventListener('item-moved', (e) => {
            console.log('Элементы перемещены');
        });
        
        desk.addEventListener('item-resized', (e) => {
            console.log('Элементы изменены');
        });
        
        // Глобальные функции для кнопок
        window.clearDesk = () => {
            desk.clearDesk();
        };
        
        window.addRandomItem = () => {
            const colors = [
                { bg: '#e3f2fd', border: '#2196f3' },
                { bg: '#fff3e0', border: '#ff9800' },
                { bg: '#f3e5f5', border: '#9c27b0' },
                { bg: '#e8f5e8', border: '#4caf50' },
                { bg: '#fff8e1', border: '#ffc107' }
            ];
            
            const icons = [
                'mdi:star', 'mdi:heart', 'mdi:lightbulb', 'mdi:rocket', 'mdi:diamond'
            ];
            
            const color = colors[Math.floor(Math.random() * colors.length)];
            const icon = icons[Math.floor(Math.random() * icons.length)];
            
            desk.addItem({
                label: `Элемент ${Date.now()}`,
                icon: icon,
                backgroundColor: color.bg,
                borderColor: color.border,
                x: Math.random() * 400,
                y: Math.random() * 300
            });
        };
        
        // Добавляем несколько примеров при загрузке
        setTimeout(() => {
            desk.addItem({
                label: 'Добро пожаловать!',
                icon: 'mdi:hand-wave',
                backgroundColor: '#e8f5e8',
                borderColor: '#4caf50',
                x: 100,
                y: 100,
                width: 160,
                height: 100
            });
            
            desk.addItem({
                label: 'Рабочий стол FX',
                icon: 'mdi:desktop-mac',
                backgroundColor: '#e3f2fd',
                borderColor: '#2196f3',
                x: 300,
                y: 150,
                width: 180,
                height: 120
            });
        }, 100);
    </script>
</body>
</html>
