<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Export/Import Database</title>
    <script type="module" src="/fx.js"></script>
    <script type="module" src="./dbs.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        #dbsComponent {
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Тест функций экспорта и импорта базы данных</h1>
        
        <div class="test-section">
            <h3>Компонент управления базами данных</h3>
            <div id="dbsComponent">
                <fx-dbs id="testDbs"></fx-dbs>
            </div>
        </div>

        <div class="test-section">
            <h3>Тестирование функций</h3>
            <div id="testStatus" class="status info">
                Готов к тестированию. Выберите базу данных выше и используйте кнопки экспорта/импорта.
            </div>
            
            <button onclick="testExportFunctionality()">Тест экспорта</button>
            <button onclick="testImportFunctionality()">Тест импорта</button>
            <button onclick="createTestData()">Создать тестовые данные</button>
            <button onclick="checkTreeIntegration()">Проверить интеграцию с Tree</button>
        </div>

        <div class="test-section">
            <h3>Инструкции по тестированию</h3>
            <ol>
                <li><strong>Создание тестовых данных:</strong> Нажмите "Создать тестовые данные" для создания локальной базы с примерами документов</li>
                <li><strong>Тест экспорта:</strong> Выберите базу данных и нажмите кнопку экспорта (зеленая иконка) в компоненте выше</li>
                <li><strong>Тест импорта:</strong> Нажмите кнопку импорта (оранжевая иконка) и выберите JSON файл для импорта</li>
                <li><strong>Экспорт выбранных элементов:</strong> Если доступен компонент Tree, отметьте элементы и выберите "Selected items only" при экспорте</li>
                <li><strong>Контекстное меню:</strong> Щелкните правой кнопкой мыши на локальной базе данных для доступа к функции экспорта</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>Лог тестирования</h3>
            <div id="testLog" style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">
                Лог будет отображаться здесь...
            </div>
        </div>
    </div>

    <script>
        let testDbs;
        
        document.addEventListener('DOMContentLoaded', () => {
            testDbs = document.getElementById('testDbs');
            log('Компонент dbs инициализирован');
            
            // Слушаем события от компонента
            testDbs.addEventListener('db-selected', (e) => {
                log(`База данных выбрана: ${e.detail.dbLocal?.name || 'неизвестно'}`);
            });
        });

        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function setStatus(message, type = 'info') {
            const statusElement = document.getElementById('testStatus');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        async function testExportFunctionality() {
            try {
                log('Начало тестирования экспорта...');
                setStatus('Тестирование экспорта...', 'info');
                
                if (!testDbs.combinedDbList || testDbs.combinedDbList.length === 0) {
                    throw new Error('Нет доступных баз данных для экспорта');
                }
                
                const selectedDb = testDbs.combinedDbList[testDbs.dbSelectedIdx];
                if (!selectedDb) {
                    throw new Error('Не выбрана база данных');
                }
                
                log(`Попытка экспорта базы: ${selectedDb.name}`);
                
                // Вызываем функцию экспорта
                await testDbs.exportDatabase('all', []);
                
                log('Экспорт завершен успешно');
                setStatus('Экспорт выполнен успешно!', 'success');
                
            } catch (error) {
                log(`Ошибка экспорта: ${error.message}`);
                setStatus(`Ошибка экспорта: ${error.message}`, 'error');
            }
        }

        async function testImportFunctionality() {
            try {
                log('Начало тестирования импорта...');
                setStatus('Выберите JSON файл для импорта...', 'info');
                
                // Создаем input для выбора файла
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.json';
                
                input.onchange = async (e) => {
                    const file = e.target.files[0];
                    if (!file) return;
                    
                    try {
                        log(`Импорт файла: ${file.name}`);
                        await testDbs.importDatabase(file, 'new', 'test_import_' + Date.now());
                        log('Импорт завершен успешно');
                        setStatus('Импорт выполнен успешно!', 'success');
                    } catch (error) {
                        log(`Ошибка импорта: ${error.message}`);
                        setStatus(`Ошибка импорта: ${error.message}`, 'error');
                    }
                };
                
                input.click();
                
            } catch (error) {
                log(`Ошибка тестирования импорта: ${error.message}`);
                setStatus(`Ошибка: ${error.message}`, 'error');
            }
        }

        async function createTestData() {
            try {
                log('Создание тестовых данных...');
                setStatus('Создание тестовой базы данных...', 'info');
                
                const testDbName = 'test_export_' + Date.now();
                await testDbs.createLocalDb(testDbName, false, '', testDbs.dbLocalPrefix);
                
                // Добавляем тестовые документы
                if (FX.PouchDB) {
                    const db = new FX.PouchDB(testDbs.dbLocalPrefix + testDbName);
                    
                    const testDocs = [
                        { _id: 'doc1', type: 'test', label: 'Тестовый документ 1', data: { value: 100 } },
                        { _id: 'doc2', type: 'test', label: 'Тестовый документ 2', data: { value: 200 } },
                        { _id: 'doc3', type: 'example', label: 'Пример документа', content: 'Это пример содержимого' }
                    ];
                    
                    for (const doc of testDocs) {
                        await db.put(doc);
                    }
                    
                    log(`Создана тестовая база "${testDbName}" с ${testDocs.length} документами`);
                    setStatus(`Тестовая база "${testDbName}" создана успешно!`, 'success');
                } else {
                    throw new Error('PouchDB не загружен');
                }
                
            } catch (error) {
                log(`Ошибка создания тестовых данных: ${error.message}`);
                setStatus(`Ошибка: ${error.message}`, 'error');
            }
        }

        function checkTreeIntegration() {
            try {
                log('Проверка интеграции с Tree компонентом...');
                
                if (testDbs.base && testDbs.base.tree) {
                    if (typeof testDbs.base.tree.getCheckedItemsIds === 'function') {
                        const checkedIds = testDbs.base.tree.getCheckedItemsIds();
                        log(`Tree интеграция работает. Выбрано элементов: ${checkedIds.length}`);
                        setStatus(`Tree интеграция работает. Выбрано: ${checkedIds.length} элементов`, 'success');
                    } else {
                        log('Метод getCheckedItemsIds не найден в Tree компоненте');
                        setStatus('Метод getCheckedItemsIds не найден', 'error');
                    }
                } else {
                    log('Tree компонент не найден или не подключен');
                    setStatus('Tree компонент недоступен', 'error');
                }
                
            } catch (error) {
                log(`Ошибка проверки Tree интеграции: ${error.message}`);
                setStatus(`Ошибка: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
