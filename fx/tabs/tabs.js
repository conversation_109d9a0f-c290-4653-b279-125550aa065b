import { FxElement, html } from '/fx.js';
import { $styles } from './tabs.x.js';
import '../icon/icon.js';

export class FxTabs extends FxElement {
    static properties = {
        isReady: { type: Boolean },
        tabsObj: { type: Object, default: null },
        tabs: { type: Array, notify: true },
        activeTab: { type: Number, default: 0, save: true },
        dragTab: { type: Object, default: null },
        dragOverIndex: { type: Number, default: -1 },
        autoScrollInterval: { type: Object, default: null },
        hideClose: { type: Boolean, default: false },
        width: { type: Number, default: 120 }
    }
    get tabs() {
        return this.tabsObj?.tabs || [];
    }
    set tabs(v) {
        if (typeof v === 'object' && (v?.id || v?.tabs)) {
            this.tabsObj = v;
        } else {
            this.tabsObj ||= {};
            this.tabsObj.tabs = v;
        }
    }
    get id() { return this.tabsObj?.id || '' }
    get activeTabName() { return this.tabs[this.activeTab]?.label || this.tabs[this.activeTab]?.title || '' }
    get activeTabId() { return this.tabs[this.activeTab]?.id || '' }
    get activeTabGroup() { return this.tabs[this.activeTab]?.group || '' }

    disconnectedCallback() {
        super.disconnectedCallback();
        if (this.autoScrollInterval) {
            clearInterval(this.autoScrollInterval);
            this.autoScrollInterval = null;
        }
    }
    firstUpdated() {
        super.firstUpdated();
        this.async(() => {
            document.body.style.opacity = 1;
            this.isReady = true;
        }, 300)
    }

    'tabs-changed'(e) {
        this.activeTab = this.activeTab > this.tabs.length - 1 ? this.tabs.length - 1 : this.activeTab;
    }

    scrollToLast() {
        this.updateComplete.then(() => {
            const tabsContainer = this.shadowRoot.querySelector('.tabs-container');
            const newTabElement = tabsContainer.children[this.tabs.length - 1];
            if (newTabElement) {
                const containerRight = tabsContainer.offsetWidth;
                const tabRight = newTabElement.offsetLeft + newTabElement.offsetWidth;

                if (tabRight > containerRight + tabsContainer.scrollLeft) {
                    tabsContainer.scrollTo({
                        left: tabRight - containerRight,
                        behavior: 'smooth'
                    })
                }
            }
        })
    }
    closeTab(index, e) {
        e.stopPropagation();
        let tab = this.tabs.splice(index, 1);
        tab = tab[0];
        this.fire('tab-closed', { tab });
        if (this.activeTab >= this.tabs.length) {
            this.activeTab = Math.max(0, this.tabs.length - 1);
        }
        this.$update();
    }
    selectTab(index, id, sure = false) {
        if (id) {
            index = this.tabs.findIndex(i => i.id === id);
            index = index === -1 ? 1 : index;
            if (!sure) {
                this.activeTab = this.activeTab >= 1 ? this.activeTab : index;
                this.activeTab = this.activeTab + 1 > this.tabs.length ? this.tabs.length - 1 : this.activeTab;
            } else {
                this.activeTab = index;
            }
        } else {
            this.activeTab = index;
        }
        this.fire('tab-selected', { index, activeTabName: this.activeTabName });
        this.$fire('tab-selected', { index, activeTabName: this.activeTabName });
        this.$update();
    }

    static styles = [$styles]

    render() {
        if (!this.tabs.length) return null;
        return html`
            <div class="tabs-container horizontal" style="overflow-x: auto; white-space: nowrap; border-bottom: 1px solid var(--fx-border-color);">
                ${this.tabs.map((tab, index) => html`
                    <div class="tab horizontal align center ${this.dragOverIndex === index ? 'drag-over' : ''} ${this.activeTab === index ? 'selected' : ''}" 
                            style="display: inline-flex; min-width: ${tab.width || this.width}px; max-width: ${tab.width || this.width}px; min-height: 24px; max-height: 24px; height: 24px; padding: 0 8px; 
                                cursor: pointer; position: relative; box-sizing: border-box;
                                background: ${this.activeTab === index ? 'var(--fx-color-selected)' : 'var(--fx-tab-background-color-active)'};
                                border-right: 1px solid var(--fx-tab-border-color);
                                transition: transform 0.2s ease;
                                box-sizing: border-box;"
                            draggable=${this.tabsObj?.noDraggable ? 'false' : 'true'}
                            @dragstart=${(e) => this.handleDragStart(e, index)}
                            @dragover=${(e) => this.handleDragOver(e, index)}
                            @dragend=${this.handleDragEnd}
                            @drop=${(e) => this.handleDrop(e, index)}
                            @click=${() => this.selectTab(index)}>
                        <fx-icon name=${tab.icon} scale=".8" class="tab-move mr4 box" style="margin-left: -6px" fill=${this.activeTab === index ? 'white' : ''}></fx-icon>
                        <span class="flex ellipsis" style="font-size: 13px;">${tab.title}</span>
                        <fx-icon name="codicon:close:20" 
                            style="opacity: 1; transition: opacity 0.2s; position: absolute; right: -2px;"
                            class="tab-close"
                            scale=".7"
                            fill= "white"
                            @click=${(e) => this.closeTab(index, e)}
                            ?hidden=${tab.ShowEvery ? false : (!tab.showClose && (this.tabsObj?.hideClose || this.hideClose || tab.hideClose))}>
                        </fx-icon>
                    </div>
                `)}
            </div>
        `
    }

    handleDragStart(e, index) {
        this.dragTab = this.tabs[index];
        e.dataTransfer.effectAllowed = 'move';
        e.target.classList.add('dragging');
    }
    handleDragOver(e, index) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
        this.dragOverIndex = index;
        const tabsContainer = this.shadowRoot.querySelector('.tabs-container');
        const containerRect = tabsContainer.getBoundingClientRect();
        const scrollSpeed = 15;
        if (this.autoScrollInterval) {
            clearInterval(this.autoScrollInterval);
            this.autoScrollInterval = null;
        }
        const distanceFromLeft = e.clientX - containerRect.left;
        const distanceFromRight = containerRect.right - e.clientX;
        const scrollTriggerDistance = 50;
        if (distanceFromLeft < scrollTriggerDistance) {
            this.autoScrollInterval = setInterval(() => {
                tabsContainer.scrollLeft -= scrollSpeed;
            }, 16);
        } else if (distanceFromRight < scrollTriggerDistance) {
            this.autoScrollInterval = setInterval(() => {
                tabsContainer.scrollLeft += scrollSpeed;
            }, 16);
        }
    }
    handleDragEnd(e) {
        e.target.classList.remove('dragging');
        this.dragOverIndex = -1;
        if (this.autoScrollInterval) {
            clearInterval(this.autoScrollInterval);
            this.autoScrollInterval = null;
        }
    }
    handleDrop(e, dropIndex) {
        e.preventDefault();
        const dragIndex = this.tabs.indexOf(this.dragTab);
        if (dragIndex === dropIndex) return;
        this.tabs.splice(dragIndex, 1);
        this.tabs.splice(dropIndex, 0, this.dragTab);
        if (this.activeTab === dragIndex) {
            this.activeTab = dropIndex;
        } else if (this.activeTab > dragIndex && this.activeTab <= dropIndex) {
            this.activeTab--;
        } else if (this.activeTab < dragIndex && this.activeTab >= dropIndex) {
            this.activeTab++;
        }
        this.dragTab = null;
        this.dragOverIndex = -1;
    }
}
customElements.define('fx-tabs', FxTabs)
