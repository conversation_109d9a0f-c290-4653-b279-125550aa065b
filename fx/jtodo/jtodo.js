import { FxElement, html, css } from '/fx.js';

import '../icon/icon.js';
import { $styles } from './jtodo-h.js';
import '../button/button.js';
import '../checkbox/checkbox.js';
import '../rating/rating.js';
// import '../editor/editor.js';

customElements.define('fx-jtodo', class FxJToDo extends FxElement {
    static properties = {
        src: { type: Object, default: { items: [] } },
        cell: { type: Object, default: undefined, notify: true },
        editMode: { type: Object },
        _rating: { type: Number, default: 5 }
    }
    get rating() { return +(this.cell?.rating || this._rating) }

    firstUpdated() {
        super.firstUpdated();
    }

    setRating(e) {
        this._rating = e.target.value;
        if (this.cell)
            this.cell.rating = this._rating;
        this.fire('change', { src: this.src });
        this.$update();
    }
    setEditMode() {
        this.editMode = !this.editMode;
        this.$update();
    }
    'cell-changed'() {
        if (this.cell) {
            this.src = this.cell.source || { items: [] };
            this._update();
        }
    }
    async showEditor(i, src, idx) {
        const jhtml = await FX.createComponent('jhtml');
        jhtml.listen('change', (e) => {
            i.results[idx] = e.detail || '';
        })  
        let res = await FX.show('dropdown', jhtml, { src, isEditorOnly: true }, { showHeader: true, align: 'full' }, 'fx', 'fx');
        // console.log(res)
        this.fire('change', { src: this.src });
        if (this.cell)
            this.cell.source = this.src;
        this._update();
    }
    async deleteResult(i, idx) {
        i.results.splice(idx, 1);
        this.fire('change', { src: this.src });
        if (this.cell)
            this.cell.source = this.src;
        this._update();
    }
    onclickAdd(e, i) {
        this.src.items.push({
            "todo": 'new todo',
            'check': false
        })
        this.fire('change', { src: this.src });
        if (this.cell)
            this.cell.source = this.src;
        this._update();
    }
    oncheck(e, i) {
        i.check = e.target.toggled;
        this.fire('change', { src: this.src });
        if (this.cell)
            this.cell.source = this.src;
        this._update()
    }
    addResult(e, i) {
        i.results ||= [];
        i.results.push('');
        this.fire('change', { src: this.src });
        if (this.cell)
            this.cell.source = this.src;
        this._update()
    }
    async onclickClose(e, i) {
        let idx = this.src.items.indexOf(i);
        this.src.items.splice(idx, 1);
        this.fire('change', { src: this.src });
        if (this.cell)
            this.cell.source = this.src;
        this._update();
    }
    _update() {
        this.$update();
        FX.$00?.$update();
    }
    _change(e, i) {
        i.todo = e.target.value || '';
        this.fire('change', { src: this.src });
        if (this.cell)
            this.cell.source = this.src;
        this._update();
    }
    _changeResult(e, i, idx) {
        i.results[idx] = e.target.innerHTML || '';
        this.fire('change', { src: this.src });
        if (this.cell)
            this.cell.source = this.src;
        this._update();
    }
    changeTodoLabel(e) {
        if (this.cell) {
            this.cell.todoLabel = e.target.value;
            this.fire('change', { src: this.cell });
        }
        this._update();
    }
    setHide(i) {
        i.hide = !i.hide;
        this.fire('change', { src: this.src });
        if (this.cell)
            this.cell.source = this.src;
        this._update();
    }

    static styles = [$styles.main]

    render() {
        return html`
            <div class="vertical flex overflow-h h100 w100 relative">
                <div class="panel horizontal center bral m4 p4" style="z-index: 1">
                    <input class="inp" style="font-size: large; width: 100%;" .value="${this.cell?.todoLabel || 'ToDo'}" @blur=${this.changeTodoLabel}>
                    <select class="fm brb" @change=${this.setRating} style="color: gray; width: 48px; border: none; outline: none; background: transparent;" .hidden=${!this.editMode}>
                        <option value=0 .selected=${this.rating === 0}>0</option>
                        <option value=1 .selected=${this.rating === 1}>1</option>
                        <option value=2 .selected=${this.rating === 2}>2</option>
                        <option value=3 .selected=${this.rating === 3}>3</option>
                        <option value=4 .selected=${this.rating === 4}>4</option>
                        <option value=5 .selected=${this.rating === 5}>5</option>
                        <option value=6 .selected=${this.rating === 6}>6</option>
                        <option value=7 .selected=${this.rating === 7}>7</option>
                        <option value=8 .selected=${this.rating === 8}>8</option>
                        <option value=9 .selected=${this.rating === 9}>9</option>
                        <option value=10 .selected=${this.rating === 10}>10</option>
                    </select>
                    <fx-icon id="edit" url="carbon:add" class="butn ml4 mr4" size="20" an="btn" br="square" scale="1" title="edit" @click=${this.onclickAdd} fill="red" ?hidden=${!this.editMode}></fx-icon>
                    <fx-icon id="edit" url="carbon:edit" class="icon but ml4 mr4" size="20" an="btn" br="square" scale="1" title="edit" @click=${this.setEditMode}  fill="green" back=${this.editMode ? 'yellow' : ''} style="opacity: ${this.editMode ? 1 : .3}"></fx-icon>
                    <fx-icon id="edit" url="fc-idea" class="icon but ml4 mr4" size="20" an="btn" br="circle" scale="1" title="edit" @click=${e => { this.showAll = !this.showAll; this.$update(); }} style="opacity: ${this.showAll ? 1 : .3}"></fx-icon>
                </div>
                <div class="vertical flex overflow-y h100 w100 relative">
                    ${(this.src || []).items.map(i => !this.showAll && i.hide ? html`` : html`
                        <div class="vertical relative bral m4">
                            <div class="horizontal center wrap p2" style="border-bottom: ${i.results?.length ? '1px solid lightgray' : ''}; background: ${i.check ? 'honeydew' : 'lavenderblush'}">
                                <fx-checkbox class="ml4 mr4" fill=${i.check ? 'green' : 'lightgray'} @click=${e => this.oncheck(e, i)} ?toggled=${i.check} style="opacity: .8" title="is Ok"></fx-checkbox>
                                <input ?readonly=${!this.editMode} class="inp flex fm" .value=${i.todo} @blur=${e => this._change(e, i)} style="color: #333">
                                <fx-rating class="ml4 mr4 horizontal center" length=${this.rating} size=16 value=${i.rating} @change=${e => i.rating = e.target.value} style="min-width: ${this.rating * 18}px" .hidden=${this.rating === 0 || this.rating === '0'}></fx-rating>
                                <fx-icon class="icon" icon='fc-plus' @click=${e => this.addResult(e, i)} title="add result" .hidden=${!this.editMode}></fx-icon>
                                <fx-icon class="icon" name="cb-close-outline" @click=${e => this.onclickClose(e, i)} fill="red" .hidden=${!this.editMode}></fx-icon>
                                <fx-icon class="icon" icon=${i.hide ? 'flat-color-icons:no-idea' : 'fc-idea'} @click=${e => this.setHide(i)} style="margin-top: -4px; opacity: ${i.hide ? .2 : .8}" .hidden=${!this.editMode && !i.hide}></fx-icon>
                            </div>
                            ${(i.results || []).map((r, idx) => html`
                                <div class="horizontal w100 relative" style="border-bottom: ${i.results?.length - 1 !== idx ? '1px dashed gray' : ''}">
                                    <div ?contenteditable=${this.editMode} class="row overflow" @blur=${e => this._changeResult(e, i, idx)} style="overflow-wrap: anywhere;" spellcheck="false" .innerHTML=${r || ''}></div>
                                    <fx-icon size="24" class="sets pointer absolute" icon="cb-edit" an="btn2" style="right: 28px; top: 4px;" @click=${e => this.showEditor(i, r, idx)} .hidden=${!this.editMode}></fx-icon>
                                    <fx-icon size="24" class="sets pointer absolute" icon="cb-close-outline" an="btn2" fill="red" style="right: 4px; top: 4px;" @click=${e => this.deleteResult(i, idx)} .hidden=${!this.editMode}></fx-icon>
                                </div>
                            `)}
                        </div>
                    `)}
                    <div class="flex"></div>
                </div>
            </div>
        `;
    }
})
