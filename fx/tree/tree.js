import { FxElement, html } from '/fx.js';
import { $styles } from './tree.x.js';
import '../base/src/base-item.js';
import '../icon/icon.js';

export class FxTree extends FxElement {
    static properties = {
        readOnly: { type: Boolean },
        isReady: { type: Boolean },
        id: { type: String, default: '', notify: true },
        item: { type: Object, default: {} },
        fontSize: { type: String, default: 'medium' },
        selected: { type: Object, default: null },
        draggedItem: { type: Object, default: null },
        dropTarget: { type: Object, default: null },
        selectedID: { type: String, default: '', save: true },
        rootID: { type: String, default: '', save: true },
        expanded: { type: Array, default: [], save: true },
        _selectedID: { type: String, default: '', save: true },
        _rootID: { type: String, default: '', save: true },
        _expanded: { type: Array, default: [], save: true },
        hide: { type: String, default: '' },
        plainTextMode: { type: Boolean, default: true, save: true },
        singleCheckMode: { type: Boolean, default: false },
        singleCheckModeColor: { type: String, default: 'var(--fx-color-selected)' },
    }
    get flatItems() { return FX.flatItems(this.item, true) || {} }
    get allItems() { return FX.allItems(this.item) || [] }
    get toDelete() { return FX.toDelete(this.item, true) || [] }

    get hideTop() { return this.hide?.includes('t') }
    get hideAdd() { return this.hide?.includes('a') }
    get hideCheck() { return this.hide?.includes('c') }
    get hidePhoto() { return this.hide?.includes('p') }
    get hidePlainMode() { return this.hide?.includes('m') }

    firstUpdated() {
        super.firstUpdated();
        this.restoreTreeState();
    }

    'id-changed'(e, _rootID) {
        if (!e.startsWith('undefined') && this.isReady) {
            this._rootID ||= _rootID;
            this.restoreTreeState();
        }
    }
    restoreTreeState() {
        if (this.flatItems) {
            this.async(() => {
                this.isReady = false;
                this._updateSaves();
                this.allItems.map(i => i.expanded = false);
                const exp = this._selectedID ? (this._expanded || []) : (this.expanded || []);
                (exp).map(i => {
                    let item = this.flatItems[i];
                    if (item) item.expanded = true;
                })
                let item = this.flatItems[this._selectedID || this.selectedID] || this.item;
                // item.expanded = true;
                this._select(null, item);
                this._selectedID = '';
                this._root = this.flatItems[this._rootID || this.rootID];
                this._rootID = '';
                this.style.opacity = 1;
                this.isReady = true;
                this.$update();
            }, 20)
        }
    }
    selectById(id) {
        let item = this.flatItems[id] || this.item;
        this._select(null, item);
    }
    storeTreeState(e, tempStore) {
        this.fire('storeTreeState', { item: this.item, selected: this.selected, id: this.id });
        const expanded = [];
        this.allItems.map(i => { if (i.expanded) expanded.push(i._id || i.id) });
        const id = this.selected?._id || this.selected?.id || this.item?._id || '';
        if (tempStore) {
            this._expanded = expanded;
            this._selectedID = id;
            this._rootID = this._root?._id || this._root?.id || '';
        } else {
            this.expanded = expanded;
            this.selectedID = id;
            this.rootID = this._root?._id || this._root?.id || '';
        }
        this.$update();
    }
    _select(e, item) {
        if (!item) return;
        this.selected = item;
        this.fire('selected', { item, id: this.id });
        this.async(() => this.$update(), 20);
        this.$update();
    }
    toggleAllChecked(item, checked) {
        item.checked = checked;
        if (this.singleCheckMode) {
            this.fire('changed', { type: 'setTreeChecked', value: checked, item, id: this.id });
            return;
        }
        if (item.items && item.items.length > 0) {
            item.items.forEach(child => this.toggleAllChecked(child, checked));
        }
    }
    toggleExpand(item) {
        item.expanded = !item.expanded;
        this.$update();
    }
    toggleExpandAll(item, expand) {
        if (item.items && item.items.length > 0) {
            item.expanded = expand;
            item.items.forEach(child => this.toggleExpandAll(child, expand));
        }
        this.$update();
    }
    clearChecked() {
        this.allItems.map(i => i.checked = false);
        this.$update();
    }
    getCheckedItems() {
        return this.allItems.filter(i => i.checked);
    }
    getCheckedItemsIds() {
        return this.allItems.filter(i => i.checked).map(i => i._id || i.id);
    }
    setRoot() {
        let id = this.selected?._id || this.selected?.id;
        if (this.selected?.items) {
            this._root = (this._root?._id || this._root?.id) === id ? undefined : this.selected;
        } else {
            this._root = undefined;
        }
        this.$update();
    }
    addItem() {
        this.fire('add-item', { item: this.item, selected: this.selected, id: this.id });
    }
    delete() {
        if (this.toDelete.length > 0) {
            this.allItems.map(i => i._deleted = false);
        } else {
            this.allItems.map(i => { if (i.checked) i._deleted = true });
            delete this.item._deleted;
        }
        this.fire('delete', { item: this.item, selected: this.selected, id: this.id, toDelete: this.toDelete });
        this.$update();
    }
    toggleTextMode() {
        this.plainTextMode = !this.plainTextMode;
        this.$update();
    }
    setLabel(e, item) {
        const label = e.target.value;
        if (label !== item.label) {
            if (this.base) {
                this.base.bsSelected.doc.label = label;
            }
            item.label = label;
            this.fire('setlabel', label);
            this.fire('changed', { type: 'setTreeLabel', value: label, item, id: this.id });
        }
        this.$update();
    }

    static styles = [$styles]

    renderTreeItem(item, level = 0) {
        const hasChildren = item.items && item.items.length > 0,
            selected = this.selected === item || this.selected?.id && this.selected?.id === item?.id,
            label = item.label || item.name || item.id || '';
        return html`
            <div class="tree-item"
                @dragover=${(e) => this.handleDragOver(e, item)}
                @dragleave=${this.handleDragLeave}
                @dragend=${this.handleDragEnd}
                @drop=${(e) => this.handleDrop(e, item)}>
                <div class="horizontal align tree-content w100 ${selected ? 'selected' : ''}">
                    <div class="drop horizontal pointer" 
                        style="min-width: 24px; max-width: 24px; height: 24px;"
                        draggable=${this.readOnly || this.item.readOnly ? 'false' : 'true'}
                        @dragstart=${(e) => this.handleDragStart(e, item)}>
                        ${hasChildren ? html`
                            <fx-icon class="pl2" url="cb-chevron-right"
                                @click=${() => this.toggleExpand(item)}
                                style="transform: rotate(${item.expanded ? '90deg' : '0deg'}); transition: transform 0.2s;"
                            ></fx-icon>
                        ` : html``}
                    </div>
                    ${this.hideCheck || this.item.hideCheck ? html`` : html`
                        <fx-icon class="check" fill= ${selected ? 'white' : this.singleCheckMode ? this.singleCheckModeColor : ''}
                            url=${item.checked ? 'carbon:checkbox-checked-filled' : 'carbon:checkbox'} an="btn2"
                            style="opacity: .5;"
                            @click=${e => {
                                const newCheckedState = !item.checked;
                                this.toggleAllChecked(item, newCheckedState);
                                this.$update();
                            }}
                        ></fx-icon>
                    `}
                    ${item.icon ? html`
                        <fx-icon url="${item.icon}" @click=${(e) => this._select(e, item)} size=${item.iconSize || 24}></fx-icon>
                    ` : ''}
                        ${this.plainTextMode ? html`
                            <input 
                                class="flex inp p2 ellipsis w100 align" 
                                title=${label}
                                ?readonly=${!selected || this.readOnly || this.item.readOnly}
                                @click=${(e) => this._select(e, item)} 
                                @blur=${e => this.setLabel(e, item)}
                                .value="${label}"
                                style="font-size:${this.fontSize}; min-width: 0px!important; width: 100%; 
                                    vertical-align: middle; display: block;
                                    background: transparent; border: none; outline: none;
                                    color: ${this.selected === item ? 'white' : 'var(--fx-color)'};"
                            />
                        ` : html`
                            <div 
                                class="flex inp p2 ellipsis w100 align" 
                                title=${label}
                                @click=${(e) => this._select(e, item)}
                                style="font-size:${this.fontSize}; min-width: 0px!important; width: 100%; 
                                    vertical-align: middle; display: block;
                                    background: transparent; border: none; outline: none; text-wrap: auto;
                                    color: ${this.selected === item ? 'white' : 'var(--fx-color)'};"
                            >${label}</div>
                        `}

                     ${item._deleted ? html`
                        <fx-icon class="z-9999 mr4" an="btn" url="fx:close:18" fill="red" @click=${e => { item._deleted = false; this.$update() }}></fx-icon>
                    ` : html``}
                </div>
            </div>
            ${hasChildren && item.expanded ? html`
                <div class="tree-children">
                    ${item.items.map(child => this.renderTreeItem(child, level + 1))}
                </div>
            ` : ''}
        `;
    }

    render() {
        if (!this.item) return html``;
        return html`
            <div class="main vertical flex h100 overflow-h">
                ${this.hideTop ? html`` : html`
                    <div class="top-panel panel horizontal w100 relative brb p4 box align wrap">
                        <div class="horizontal flex">
                            <fx-icon class="ml2 mr8" url="carbon:collapse-categories" scale=.9 an="btn" @click=${e => this.toggleExpandAll(this.selected, false)}></fx-icon>
                            <fx-icon class="mr8" url="carbon:expand-categories" scale=.9 an="btn" @click=${e => this.toggleExpandAll(this.selected, true)}></fx-icon>
                            <fx-icon back=${this._root ? 'var(--fx-color-selected)' : ''} class="mr8" url="carbon:airport-location" scale=.9 an="btn" @click=${this.setRoot}></fx-icon>
                            ${this.hidePlainMode ? html`` : html`
                                <fx-icon class="mr8" an="btn" scale=1
                                    url=${this.plainTextMode ? "carbon:text-annotation-toggle" : "carbon:text-font"} 
                                    @click=${this.toggleTextMode} 
                                    title=${this.plainTextMode ? "editable mode" : "full text mode"}
                                ></fx-icon>
                            `}
                            ${this.hidePhoto ? html`` : html`<fx-icon class="mr8" url="pepicons-pencil:photo-camera" scale=1 an="btn" @click=${this.storeTreeState}></fx-icon>`}
                        </div> 
                        <div class="horizontal flex">
                            <slot class="horizontal w100 relative" name="panel"></slot>
                            ${this.hideAdd ? html`` : html`
                            <div class="horizontal box">
                                <fx-icon class="but ml8" url="carbon:close" fill="red" scale=1 an="btn" br="square" @click=${this.delete} style="background: ${this.toDelete.length > 0 ? 'lightyellow' : ''}"></fx-icon>
                                <fx-icon class="but ml8 mr2" url="carbon:add" fill="blue" scale=1 an="btn" br="square" @click=${this.addItem}></fx-icon>
                            </div>`}
                        </div>  
                    </div>`
            }
                <div class="tree-container grid flex w100 relative overflow-y">
                    ${this.renderTreeItem(this._root || this.item)}
                    <div class="mt-1 h8 brt"></div>
                </div>
            </div>
        `;
    }

    findParent(root, targetItem) {
        if (!root.items) return null;
        for (const item of root.items) {
            if (item === targetItem) return root;
            const found = this.findParent(item, targetItem);
            if (found) return found;
        }
        return null;
    }
    handleDragStart(e, item) {
        this.draggedItem = item;
        e.dataTransfer.effectAllowed = 'move';
        e.target.classList.add('dragging');
        e.dataTransfer.setData('text/plain', this.id);
    }
    isDescendant(parent, child) {
        if (!parent.items) return false;
        for (const item of parent.items) {
            if (item === child) return true;
            if (this.isDescendant(item, child)) return true;
        }
        return false;
    }
    handleDragOver(e, item) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
        if (item === this.draggedItem || this.isDescendant(this.draggedItem, item)) {
            e.currentTarget.classList.remove('drop-above', 'drop-below', 'drop-into');
            return;
        }
        const rect = e.currentTarget.getBoundingClientRect();
        const midY = rect.top + rect.height / 2;
        e.currentTarget.classList.remove('drop-above', 'drop-below', 'drop-into');
        if (e.clientY < midY - rect.height / 4) {
            e.currentTarget.classList.add('drop-above');
            this.dropPosition = 'above';
        } else if (e.clientY > midY + rect.height / 4) {
            e.currentTarget.classList.add('drop-below');
            this.dropPosition = 'below';
        } else {
            e.currentTarget.classList.add('drop-into');
            this.dropPosition = 'into';
        }
        this.dropTarget = item;
    }
    handleDragLeave(e) {
        e.currentTarget.classList.remove('drop-above', 'drop-below', 'drop-into');
    }
    handleDragEnd(e) {
        e.target.classList.remove('dragging');
        let draggedItem = this.draggedItem;
        this.draggedItem = null;
        this.dropTarget = null;
        this.shadowRoot.querySelectorAll('.tree-item').forEach(item => {
            item.classList.remove('drop-above', 'drop-below', 'drop-into');
        })
        this.fire('changed', { type: 'moveTreeRow', item: this.item, draggedItem, originalEvent: e });
        this.$fire('changed', { type: 'moveTreeRow', item: this.item, draggedItem, originalEvent: e  });
    }
    handleDrop(e, targetItem) {
        e.preventDefault();
        if (!this.draggedItem || !this.dropTarget) return;
        if (targetItem === this.draggedItem || this.isDescendant(this.draggedItem, targetItem)) {
            this.handleDragEnd(e);
            return;
        }
        const sourceParent = this.findParent(this.item, this.draggedItem);
        const targetParent = this.findParent(this.item, targetItem);

        if (sourceParent) {
            sourceParent.items = sourceParent.items.filter(item => item !== this.draggedItem);
        } else {
            this.item.items = this.item.items.filter(item => item !== this.draggedItem);
        }
        switch (this.dropPosition) {
            case 'above':
            case 'below': {
                const targetParentItems = targetParent ? targetParent.items : this.item.items;
                const targetIndex = targetParentItems.indexOf(targetItem);
                const insertIndex = this.dropPosition === 'above' ? targetIndex : targetIndex + 1;
                targetParentItems.splice(insertIndex, 0, this.draggedItem);
                break;
            }
            case 'into': {
                targetItem.items = targetItem.items || [];
                targetItem.items.push(this.draggedItem);
                targetItem.expanded = true;
                break;
            }
        }
        this.handleDragEnd(e);
        this.$update();
    }
}
customElements.define('fx-tree', FxTree)
